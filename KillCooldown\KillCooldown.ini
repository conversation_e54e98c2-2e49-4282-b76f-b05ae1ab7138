[KillCooldown]
; Enable/disable the plugin (1=enabled, 0=disabled)
Enabled=1

; Save interval in milliseconds (how often to save cooldown data)
SaveInterval=30000

; Ignore latest enemy pointer to avoid crashes (1=enabled, 0=disabled)
IgnoreLatestEnemy=1

; Strict concurrency protection (1=enabled, 0=disabled)
StrictConcurrencyProtection=1

[CONTRIBUTION_SYSTEM]
; Enable contribution-based reward system (1=enabled, 0=disabled)
Enabled=1

; Time window in milliseconds - attackers must contribute within this time before enemy death
ContributionTimeWindow=10000

; Minimum damage percentage required to get rewards (5.0 = 5% of enemy MaxHP)
MinContributionPercent=5.0

; Enable detailed contribution logging (1=enabled, 0=disabled)
LogActivity=1

[MAXHP_DETECTION]
; Enable MaxHP detection and logging (1=enabled, 0=disabled)
EnableDetection=1

; Log MaxHP detection attempts (1=enabled, 0=disabled)
LogDetection=1

; Default HP values for estimation when exact MaxHP cannot be found
DefaultPlayerHP=50000
DefaultMonsterHP=10000
DefaultBossHP=100000

[LOGGING]
; Enable general threat activity logging (1=enabled, 0=disabled)
EnableLogging=1

; Log file will be created in: Release/logs/threat_activity.log
; Log contribution details (1=enabled, 0=disabled)
LogContributions=1

; Log reward filtering (1=enabled, 0=disabled)
LogRewardFiltering=1

; Log MaxHP detection (1=enabled, 0=disabled)
LogMaxHPDetection=1
