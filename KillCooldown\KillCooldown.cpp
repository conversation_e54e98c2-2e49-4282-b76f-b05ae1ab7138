#define WIN32_LEAN_AND_MEAN
#include <Windows.h>
#include <Psapi.h>
#include "../Header/detours.h"
#include "../Header/License.h"
#include <fstream>
#include <string>
#include <mutex>
#include <unordered_map>
#include <vector>
#include <cstdarg>

#pragma comment(lib, "Psapi.lib")

// Forward declarations
struct SaveEnemyInfo;
void AddContribution(DWORD victimCID, DWORD attackerCID, int damageAmount, void *pVictimThreat = nullptr, void *pAttackerCreature = nullptr);
DWORD GetCreatureMaxHP(void *pCreature);

// Safe memory reading function
bool SafeReadMemory(PBYTE pAddress, void *pBuffer, SIZE_T nSize)
{
    if (!pAddress || !pBuffer || nSize == 0)
        return false;
    if (IsBadReadPtr(pAddress, nSize))
        return false;
    try
    {
        memcpy(pBuff<PERSON>, pAddress, nSize);
        return true;
    }
    catch (...)
    {
        return false;
    }
}

// Global variables
std::string g_CooldownFilePath;
std::string g_ConfigFilePath;
std::string g_ThreatLogFilePath;

// Configuration values
bool g_Enabled = true;
DWORD g_CooldownTime = 30000;              // 30 seconds in milliseconds
DWORD g_SaveInterval = 30000;              // 30 seconds in milliseconds
bool g_IgnoreLatestEnemy = true;           // Avoid crashes with m_LatestEnemy pointer
bool g_StrictConcurrencyProtection = true; // Enable thread safety protection

// Contribution system configuration
bool g_EnableContributionSystem = true;
DWORD g_ContributionTimeWindow = 10000; // 10 seconds - must contribute within this time before death
float g_MinContributionPercent = 5.0f;  // Minimum 5% damage to get rewards

// MaxHP detection defaults
DWORD g_DefaultPlayerHP = 50000;  // Default HP for players
DWORD g_DefaultMonsterHP = 10000; // Default HP for monsters
DWORD g_DefaultBossHP = 100000;   // Default HP for bosses

// Mutex for thread-safe data access
std::mutex g_CooldownMutex;
std::mutex g_ContributionMutex;

// Persistent cooldown storage - maps character ID to last kill timestamp
std::unordered_map<DWORD, DWORD> g_PersistentCooldowns;

// Contribution tracking structure
struct ContributionInfo
{
    DWORD m_dwAttackerCID;        // Who attacked
    DWORD m_dwVictimCID;          // Who was attacked
    DWORD m_dwFirstAttack;        // First attack timestamp
    DWORD m_dwLastContribution;   // Last contribution timestamp
    DWORD m_dwTotalDamage;        // Total damage dealt to this victim
    DWORD m_dwVictimMaxHP;        // Victim's maximum HP
    float m_fContributionPercent; // Damage percentage (totalDamage / maxHP * 100)
    void *m_pVictimThreat;        // Pointer to victim's CThreat object
    void *m_pAttackerCreature;    // Pointer to attacker's creature object
};

// Storage for contributions - maps victim CID to list of contributors
std::unordered_map<DWORD, std::vector<ContributionInfo>> g_Contributions;

// SaveEnemyInfo structure
struct SaveEnemyInfo
{
    DWORD m_dwCID;       // Character ID
    DWORD m_dwTickCount; // Timestamp
};

// Function pointer types
typedef char(__thiscall *tSaveEnemy)(void *pThis, unsigned int dwCID);
typedef void(__thiscall *tAddToThreatList)(void *pThis, void *pAttackCreature, int lThreatAmount);
typedef char(__thiscall *tDivisionFame)(void *pThis);

tSaveEnemy oSaveEnemy = nullptr;
tAddToThreatList oAddToThreatList = nullptr;
tDivisionFame oDivisionFame = nullptr;

// Original bytes storage for hook restoration
BYTE g_OriginalBytes[6];

// Logging function
void LogThreatActivity(const char *format, ...)
{
    if (g_ThreatLogFilePath.empty())
        return;

    try
    {
        // Get current time
        SYSTEMTIME st;
        GetLocalTime(&st);

        // Format timestamp
        char timestamp[64];
        sprintf_s(timestamp, "[%04d-%02d-%02d %02d:%02d:%02d.%03d]",
                  st.wYear, st.wMonth, st.wDay, st.wHour, st.wMinute, st.wSecond, st.wMilliseconds);

        // Format message
        char message[1024];
        va_list args;
        va_start(args, format);
        vsnprintf_s(message, sizeof(message), _TRUNCATE, format, args);
        va_end(args);

        // Write to file
        std::ofstream logFile(g_ThreatLogFilePath, std::ios::app);
        if (logFile.is_open())
        {
            logFile << timestamp << " THREAT: " << message << std::endl;
            logFile.close();
        }
    }
    catch (...)
    {
        // Silent error handling
    }
}

// INI reading function
int ReadIniInt(const std::string &filePath, const std::string &section, const std::string &key, int defaultValue)
{
    return GetPrivateProfileIntA(section.c_str(), key.c_str(), defaultValue, filePath.c_str());
}

// Function to get creature's max HP
DWORD GetCreatureMaxHP(void *pCreature)
{
    if (!pCreature)
        return g_DefaultMonsterHP;

    try
    {
        DWORD maxHP = 0;
        DWORD offsets[] = {0x14, 0x18, 0x1C, 0x24, 0x28, 0x2C, 0x30, 0x34};

        for (int i = 0; i < sizeof(offsets) / sizeof(DWORD); i++)
        {
            if (SafeReadMemory((PBYTE)pCreature + offsets[i], &maxHP, sizeof(DWORD)))
            {
                if (maxHP >= 100 && maxHP <= 10000000)
                {
                    return maxHP;
                }
            }
        }

        // Estimate based on CID type
        DWORD cid = 0;
        if (SafeReadMemory((PBYTE)pCreature + 0x20, &cid, sizeof(DWORD)))
        {
            if (cid >= 1 && cid <= 100)
                return g_DefaultPlayerHP;
            if (cid >= 101 && cid <= 1000)
                return g_DefaultMonsterHP;
            if (cid >= 1001)
                return g_DefaultBossHP;
        }

        return g_DefaultMonsterHP;
    }
    catch (...)
    {
        return g_DefaultMonsterHP;
    }
}

// Function to add or update contribution information
void AddContribution(DWORD victimCID, DWORD attackerCID, int damageAmount, void *pVictimThreat, void *pAttackerCreature)
{
    if (!g_EnableContributionSystem)
        return;

    try
    {
        std::lock_guard<std::mutex> lock(g_ContributionMutex);
        DWORD currentTime = GetTickCount();
        DWORD victimMaxHP = GetCreatureMaxHP(pVictimThreat);

        auto &contributorList = g_Contributions[victimCID];

        // Look for existing contributor
        bool found = false;
        for (auto &info : contributorList)
        {
            if (info.m_dwAttackerCID == attackerCID)
            {
                info.m_dwLastContribution = currentTime;
                info.m_dwTotalDamage += damageAmount;
                info.m_dwVictimMaxHP = victimMaxHP;
                info.m_fContributionPercent = (float)info.m_dwTotalDamage / victimMaxHP * 100.0f;
                info.m_pVictimThreat = pVictimThreat;
                info.m_pAttackerCreature = pAttackerCreature;
                found = true;

                LogThreatActivity("Updated contribution: Victim=0x%08X, Attacker=0x%08X, +%d damage, Total=%d/%d, Percent=%.2f%%",
                                  victimCID, attackerCID, damageAmount, info.m_dwTotalDamage, victimMaxHP, info.m_fContributionPercent);
                break;
            }
        }

        if (!found)
        {
            ContributionInfo newInfo;
            newInfo.m_dwAttackerCID = attackerCID;
            newInfo.m_dwVictimCID = victimCID;
            newInfo.m_dwFirstAttack = currentTime;
            newInfo.m_dwLastContribution = currentTime;
            newInfo.m_dwTotalDamage = damageAmount;
            newInfo.m_dwVictimMaxHP = victimMaxHP;
            newInfo.m_fContributionPercent = (float)damageAmount / victimMaxHP * 100.0f;
            newInfo.m_pVictimThreat = pVictimThreat;
            newInfo.m_pAttackerCreature = pAttackerCreature;

            contributorList.push_back(newInfo);

            LogThreatActivity("New contributor: Victim=0x%08X, Attacker=0x%08X, Damage=%d/%d, Percent=%.2f%%",
                              victimCID, attackerCID, damageAmount, victimMaxHP, newInfo.m_fContributionPercent);
        }
    }
    catch (...)
    {
        // Silent error handling
    }
}

// Hook for CThreat::AddToThreatList - BLOCKS threats that don't meet contribution requirements
void __fastcall Hook_AddToThreatList(void *pThis, void *edx, void *pAttackCreature, int lThreatAmount)
{
    static int hookCallCount = 0;
    hookCallCount++;

    LogThreatActivity("Hook #%d: pThis=0x%08X, pAttackCreature=0x%08X, lThreatAmount=%d",
                      hookCallCount, (DWORD)pThis, (DWORD)pAttackCreature, lThreatAmount);

    // Extract CIDs for tracking
    DWORD victimCID = 0, attackerCID = 0;
    DWORD tempCID = 0;
    if (SafeReadMemory((PBYTE)pThis - 0x30 + 0x20, &tempCID, sizeof(DWORD)) && tempCID != 0)
        victimCID = tempCID;
    if (SafeReadMemory((PBYTE)pAttackCreature + 0x20, &tempCID, sizeof(DWORD)) && tempCID != 0)
        attackerCID = tempCID;

    bool shouldBlockThreat = false;

    if (g_EnableContributionSystem && victimCID != 0 && attackerCID != 0)
    {
        // Track/update contribution FIRST
        AddContribution(victimCID, attackerCID, lThreatAmount, pThis, pAttackCreature);

        // Check if this attacker meets contribution requirements
        try
        {
            std::lock_guard<std::mutex> lock(g_ContributionMutex);
            auto victimIt = g_Contributions.find(victimCID);

            if (victimIt != g_Contributions.end())
            {
                DWORD currentTime = GetTickCount();

                for (const auto &info : victimIt->second)
                {
                    if (info.m_dwAttackerCID == attackerCID)
                    {
                        DWORD timeSinceFirstAttack = currentTime - info.m_dwFirstAttack;

                        // Block threat if:
                        // 1. Contribution is too low (< 5%) AND
                        // 2. Attacker has been attacking for more than 10 seconds
                        if (info.m_fContributionPercent < g_MinContributionPercent &&
                            timeSinceFirstAttack > g_ContributionTimeWindow)
                        {
                            shouldBlockThreat = true;
                            LogThreatActivity("BLOCKING THREAT: Attacker=0x%08X, Contribution=%.2f%% (< %.1f%%), AttackTime=%dms (> %dms)",
                                              attackerCID, info.m_fContributionPercent, g_MinContributionPercent,
                                              timeSinceFirstAttack, g_ContributionTimeWindow);
                        }
                        else if (info.m_fContributionPercent >= g_MinContributionPercent)
                        {
                            LogThreatActivity("ALLOWING THREAT: Attacker=0x%08X, Contribution=%.2f%% (meets requirement)",
                                              attackerCID, info.m_fContributionPercent);
                        }
                        else
                        {
                            LogThreatActivity("ALLOWING THREAT: Attacker=0x%08X, Contribution=%.2f%% (within time window %dms)",
                                              attackerCID, info.m_fContributionPercent, timeSinceFirstAttack);
                        }
                        break;
                    }
                }
            }
        }
        catch (...)
        {
            LogThreatActivity("Exception in contribution checking");
        }
    }

    // Block threat if requirements not met
    if (shouldBlockThreat)
    {
        LogThreatActivity("THREAT COMPLETELY BLOCKED: Not adding to threat list - insufficient contribution");
        return; // Don't call original function - threat is completely blocked from game
    }

    // Allow threat - call original function
    if (oAddToThreatList)
    {
        try
        {
            // Temporarily restore original bytes
            DWORD oldProtect;
            VirtualProtect((LPVOID)oAddToThreatList, 6, PAGE_EXECUTE_READWRITE, &oldProtect);
            memcpy((LPVOID)oAddToThreatList, g_OriginalBytes, 6);
            VirtualProtect((LPVOID)oAddToThreatList, 6, oldProtect, &oldProtect);

            // Call original function
            ((tAddToThreatList)oAddToThreatList)(pThis, pAttackCreature, lThreatAmount);

            // Restore our hook
            VirtualProtect((LPVOID)oAddToThreatList, 6, PAGE_EXECUTE_READWRITE, &oldProtect);
            Detours::DetourFunction((PBYTE)oAddToThreatList, (PBYTE)Hook_AddToThreatList, 6);
            VirtualProtect((LPVOID)oAddToThreatList, 6, oldProtect, &oldProtect);

            LogThreatActivity("THREAT ALLOWED: Added to threat list successfully");
        }
        catch (...)
        {
            LogThreatActivity("Exception calling original function");
        }
    }
}

// Helper function to safely access the m_LatestEnemy field
SaveEnemyInfo *GetLatestEnemyArray(void *pThis)
{
    if (!pThis)
        return nullptr;

    try
    {
        DWORD possibleOffsets[] = {0x5E0, 0x5E4, 0x5E8, 0x5EC, 0x5F0};

        for (int i = 0; i < sizeof(possibleOffsets) / sizeof(DWORD); i++)
        {
            PBYTE pOffset = (PBYTE)pThis + possibleOffsets[i];
            SaveEnemyInfo *testPtr = nullptr;

            if (SafeReadMemory(pOffset, &testPtr, sizeof(testPtr)) && testPtr)
            {
                if (!IsBadReadPtr(testPtr, sizeof(SaveEnemyInfo) * 5))
                {
                    return testPtr;
                }
            }
        }
    }
    catch (...)
    {
        // Silent error handling
    }

    return nullptr;
}

// Helper function to safely check if CID exists in array
BOOL CheckCidInArray(SaveEnemyInfo *latestEnemy, DWORD dwCID, int *foundIndex)
{
    if (!latestEnemy || !foundIndex)
        return FALSE;

    try
    {
        for (int i = 0; i < 5; i++)
        {
            if (!IsBadReadPtr(&latestEnemy[i], sizeof(SaveEnemyInfo)))
            {
                if (latestEnemy[i].m_dwCID == dwCID)
                {
                    *foundIndex = i;
                    return TRUE;
                }
            }
        }
    }
    catch (...)
    {
        // Silent error handling
    }

    return FALSE;
}

// Hook function for CThreat::SaveEnemy - PvP kill cooldowns with relog protection
char __fastcall Hook_SaveEnemy(void *pThis, void *edx, unsigned int dwCID)
{
    if (!g_Enabled || !pThis || dwCID == 0)
    {
        return oSaveEnemy ? oSaveEnemy(pThis, dwCID) : 0;
    }

    // Check if this is a player CID (players typically have CIDs 1-100)
    bool isPlayerKill = (dwCID >= 1 && dwCID <= 100);

    if (!isPlayerKill)
    {
        // Not a player kill, use original function without cooldown
        return oSaveEnemy ? oSaveEnemy(pThis, dwCID) : 0;
    }

    bool hasPersistentCooldown = false;
    char result = 0;

    try
    {
        DWORD currentTime = GetTickCount();

        // First, call original function to get the game's decision
        if (oSaveEnemy && pThis)
        {
            try
            {
                result = oSaveEnemy(pThis, dwCID);
                LogThreatActivity("PvP Kill - Original SaveEnemy result for Player CID=0x%08X: %d", dwCID, result);
            }
            catch (...)
            {
                result = 0; // Default to "new kill" if original function fails
            }
        }

        // Check persistent PvP kill cooldowns (our addition to prevent relog reset)
        {
            std::lock_guard<std::mutex> lock(g_CooldownMutex);
            auto it = g_PersistentCooldowns.find(dwCID);
            if (it != g_PersistentCooldowns.end())
            {
                DWORD timeDiff = currentTime - it->second;
                if (timeDiff < g_CooldownTime)
                {
                    hasPersistentCooldown = true;
                    LogThreatActivity("PVP COOLDOWN ACTIVE: Player CID=0x%08X, Time remaining=%dms (overriding game result)",
                                      dwCID, g_CooldownTime - timeDiff);
                    result = 1; // Override to indicate cooldown active
                }
                else
                {
                    // Cooldown expired, update timestamp if this is a new kill
                    if (result == 0) // Only update if game says it's a new kill
                    {
                        it->second = currentTime;
                        LogThreatActivity("PVP COOLDOWN EXPIRED: Player CID=0x%08X, Updated timestamp for new PvP kill", dwCID);
                    }
                }
            }
            else
            {
                // No persistent cooldown exists
                if (result == 0) // Only add if game says it's a new kill
                {
                    g_PersistentCooldowns[dwCID] = currentTime;
                    LogThreatActivity("NEW PVP COOLDOWN: Player CID=0x%08X, Started 30-second PvP kill cooldown", dwCID);
                }
            }
        }

        // Log the final decision
        if (result == 0)
        {
            LogThreatActivity("PVP KILL ALLOWED: Player CID=0x%08X (new PvP kill)", dwCID);
        }
        else
        {
            LogThreatActivity("PVP KILL BLOCKED: Player CID=0x%08X (PvP cooldown active)", dwCID);
        }
    }
    catch (...)
    {
        // Silent error handling
    }

    return result;
}

// Function to load cooldowns from file
void LoadCooldownsFromFile()
{
    try
    {
        std::ifstream file(g_CooldownFilePath);
        if (file.is_open())
        {
            std::lock_guard<std::mutex> lock(g_CooldownMutex);
            g_PersistentCooldowns.clear();

            std::string line;
            while (std::getline(file, line))
            {
                size_t pos = line.find(',');
                if (pos != std::string::npos)
                {
                    DWORD cid = std::stoul(line.substr(0, pos));
                    DWORD timestamp = std::stoul(line.substr(pos + 1));
                    g_PersistentCooldowns[cid] = timestamp;
                }
            }
            file.close();
        }
    }
    catch (...)
    {
        // Silent error handling
    }
}

// Function to save cooldowns to file
void SaveCooldownsToFile()
{
    try
    {
        std::ofstream file(g_CooldownFilePath);
        if (file.is_open())
        {
            std::lock_guard<std::mutex> lock(g_CooldownMutex);
            for (const auto &pair : g_PersistentCooldowns)
            {
                file << pair.first << "," << pair.second << std::endl;
            }
            file.close();
        }
    }
    catch (...)
    {
        // Silent error handling
    }
}

// Cooldown save thread
DWORD WINAPI CooldownSaveThread(LPVOID lpParam)
{
    while (true)
    {
        Sleep(g_SaveInterval);
        SaveCooldownsToFile();
    }
    return 0;
}

// Function to find and hook SaveEnemy
BOOL FindSaveEnemyCalls()
{
    try
    {
        DWORD possibleAddresses[] = {0x0045A930, 0x0045A950, 0x0045A910, 0x0045A900, 0x0045A940};

        for (int i = 0; i < sizeof(possibleAddresses) / sizeof(DWORD); i++)
        {
            DWORD address = possibleAddresses[i];
            oSaveEnemy = (tSaveEnemy)address;

            DWORD oldProtect;
            if (VirtualProtect((LPVOID)address, 6, PAGE_EXECUTE_READWRITE, &oldProtect))
            {
                int result = Detours::DetourFunction((PBYTE)address, (PBYTE)Hook_SaveEnemy, 6);

                if (result == DETOUR_SUCCESS)
                {
                    VirtualProtect((LPVOID)address, 6, oldProtect, &oldProtect);
                    LogThreatActivity("SaveEnemy hook installed successfully at 0x%08X", address);
                    return TRUE;
                }
                VirtualProtect((LPVOID)address, 6, oldProtect, &oldProtect);
            }
        }
    }
    catch (...)
    {
        // Silent error handling
    }

    return FALSE;
}

// Function to find and hook AddToThreatList
BOOL FindAddToThreatListCalls()
{
    try
    {
        DWORD possibleAddresses[] = {0x0045C060, 0x0045C070, 0x0045C050, 0x0045C080};

        for (int i = 0; i < sizeof(possibleAddresses) / sizeof(DWORD); i++)
        {
            DWORD address = possibleAddresses[i];

            BYTE originalBytes[6];
            if (SafeReadMemory((PBYTE)address, originalBytes, 6))
            {
                memcpy(g_OriginalBytes, originalBytes, 6);
                oAddToThreatList = (tAddToThreatList)address;

                DWORD oldProtect;
                if (VirtualProtect((LPVOID)address, 6, PAGE_EXECUTE_READWRITE, &oldProtect))
                {
                    int result = Detours::DetourFunction((PBYTE)address, (PBYTE)Hook_AddToThreatList, 6);

                    if (result == DETOUR_SUCCESS)
                    {
                        VirtualProtect((LPVOID)address, 6, oldProtect, &oldProtect);
                        LogThreatActivity("AddToThreatList hook installed successfully at address 0x%08X", address);
                        return TRUE;
                    }
                    VirtualProtect((LPVOID)address, 6, oldProtect, &oldProtect);
                }
            }
        }
    }
    catch (...)
    {
        // Silent error handling
    }

    return FALSE;
}

// Main DLL entry point
BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved)
{
    switch (ul_reason_for_call)
    {
    case DLL_PROCESS_ATTACH:
    {
        // Get module path
        char modulePath[MAX_PATH];
        GetModuleFileNameA(hModule, modulePath, MAX_PATH);
        std::string moduleDir = modulePath;
        size_t lastSlash = moduleDir.find_last_of("\\/");
        if (lastSlash != std::string::npos)
        {
            moduleDir = moduleDir.substr(0, lastSlash);
        }

        // Set up file paths
        g_CooldownFilePath = moduleDir + "\\KillCooldown.dat";
        g_ConfigFilePath = moduleDir + "\\KillCooldown.ini";
        g_ThreatLogFilePath = moduleDir + "\\..\\logs\\threat_activity.log";

        // Create logs directory if it doesn't exist
        std::string logsDir = moduleDir + "\\..\\logs";
        CreateDirectoryA(logsDir.c_str(), NULL);

        // Read configuration
        g_Enabled = (ReadIniInt(g_ConfigFilePath, "KillCooldown", "Enabled", 1) != 0);
        g_CooldownTime = ReadIniInt(g_ConfigFilePath, "KillCooldown", "CooldownTime", 30000);
        g_SaveInterval = ReadIniInt(g_ConfigFilePath, "KillCooldown", "SaveInterval", 30000);
        g_IgnoreLatestEnemy = (ReadIniInt(g_ConfigFilePath, "KillCooldown", "IgnoreLatestEnemy", 1) != 0);
        g_StrictConcurrencyProtection = (ReadIniInt(g_ConfigFilePath, "KillCooldown", "StrictConcurrencyProtection", 1) != 0);

        // Read contribution system configuration
        g_EnableContributionSystem = (ReadIniInt(g_ConfigFilePath, "CONTRIBUTION_SYSTEM", "Enabled", 1) != 0);
        g_ContributionTimeWindow = ReadIniInt(g_ConfigFilePath, "CONTRIBUTION_SYSTEM", "ContributionTimeWindow", 10000);
        g_MinContributionPercent = (float)ReadIniInt(g_ConfigFilePath, "CONTRIBUTION_SYSTEM", "MinContributionPercent", 50) / 10.0f; // 50 = 5.0%

        // Log startup information
        LogThreatActivity("=== KILLCOOLDOWN PLUGIN LOADED ===");
        LogThreatActivity("Plugin Enabled: %s", g_Enabled ? "YES" : "NO");
        LogThreatActivity("PvP Kill Cooldown Time: %d ms (%d seconds)", g_CooldownTime, g_CooldownTime / 1000);
        LogThreatActivity("Contribution System Enabled: %s", g_EnableContributionSystem ? "YES" : "NO");
        LogThreatActivity("Contribution Time Window: %d ms (%d seconds)", g_ContributionTimeWindow, g_ContributionTimeWindow / 1000);
        LogThreatActivity("Minimum Contribution Percent: %.1f%%", g_MinContributionPercent);
        LogThreatActivity("PVP COOLDOWN: 30-second cooldown between killing the same player (survives relog)");
        LogThreatActivity("THREAT BLOCKING: Enabled - threats will be blocked if contribution requirements not met");
        LogThreatActivity("================================");

        if (g_Enabled)
        {
            // Load existing cooldowns
            LoadCooldownsFromFile();

            // Install hooks
            if (FindSaveEnemyCalls())
            {
                LogThreatActivity("SaveEnemy hook installed successfully");
            }
            else
            {
                LogThreatActivity("SaveEnemy hook installation FAILED");
            }

            if (FindAddToThreatListCalls())
            {
                LogThreatActivity("AddToThreatList hook installed successfully - THREAT BLOCKING ACTIVE");
            }
            else
            {
                LogThreatActivity("AddToThreatList hook installation FAILED - THREAT BLOCKING DISABLED");
            }

            // Start save thread
            CreateThread(NULL, 0, CooldownSaveThread, NULL, 0, NULL);

            LogThreatActivity("Plugin initialization completed successfully");
        }
        else
        {
            LogThreatActivity("Plugin is DISABLED in configuration");
        }
    }
    break;

    case DLL_PROCESS_DETACH:
        // Save cooldowns on exit
        SaveCooldownsToFile();
        LogThreatActivity("Plugin unloaded, cooldowns saved");
        break;
    }

    return TRUE;
}
