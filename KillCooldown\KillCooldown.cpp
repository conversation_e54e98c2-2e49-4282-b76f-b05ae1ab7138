#define WIN32_LEAN_AND_MEAN
#include <Windows.h>
#include <Psapi.h>
#include "../Header/detours.h"
#include "../Header/License.h"
#include <fstream>
#include <string>
#include <mutex>
#include <unordered_map>
#include <unordered_set>
#include <vector>
#include <map>
#include <iomanip>
#include <sstream>
#include <chrono>
#include <thread>
#include <cstdarg>

// Forward declarations to avoid "identifier not found" errors
struct SaveEnemyInfo;
void LogThreatStatistics();
void CleanExpiredThreats();
void CleanExpiredFromGameThreatList(void *pThis);
void MarkVictimDead(DWORD victimCID, float deathPosX, float deathPosZ);
char __fastcall Hook_DivisionFame(void *pThis, void *edx);
void *__fastcall Hook_GetTarget(void *pThis, void *edx);
unsigned int *__fastcall Hook_GetAward(void *pThis, void *edx, unsigned int *dwItemKind, unsigned int *dwOwnerID);
int __fastcall Hook_CharacterDead(void *pThis, void *edx, void *pOffencer);
void __stdcall ProcessThreatHook(void *pThis, void *pAttackCreature, int lThreatAmount);

// Safe memory reading function
bool SafeReadMemory(PBYTE pAddress, void *pBuffer, SIZE_T nSize)
{
    if (!pAddress || !pBuffer || nSize == 0)
        return false;

    if (IsBadReadPtr(pAddress, nSize))
        return false;

    try
    {
        memcpy(pBuffer, pAddress, nSize);
        return true;
    }
    catch (...)
    {
        return false;
    }
}

#pragma comment(lib, "Psapi.lib")

// Global variables
DWORD g_SaveEnemyCallAddress = 0;
DWORD g_LastCallerAddress = 0;
std::string g_CooldownFilePath;
std::string g_ConfigFilePath;
std::string g_ThreatLogFilePath;

// Configuration values
bool g_Enabled = true;
DWORD g_CooldownTime = 300000;             // 5 minutes in milliseconds
DWORD g_SaveInterval = 30000;              // 30 seconds in milliseconds
bool g_IgnoreLatestEnemy = true;           // Default to true to avoid crashes with m_LatestEnemy pointer
bool g_ProcessingCall = false;             // Flag to prevent recursive/concurrent calls
bool g_StrictConcurrencyProtection = true; // Enable strict concurrency protection by default

// New threat timeout configuration
bool g_EnableThreatTimeout = true;
DWORD g_ThreatTimeout = 1800000; // 30 minutes in milliseconds
bool g_LogThreatActivity = true; // Enable detailed threat logging

// Mutex for thread-safe data access
std::mutex g_CooldownMutex;
std::mutex g_CallMutex;   // New mutex for call protection
std::mutex g_ThreatMutex; // New mutex for threat data protection

// Global variables for thread synchronization
bool g_SaveRequested = false;

// Persistent cooldown storage - maps character ID to last kill timestamp
std::unordered_map<DWORD, DWORD> g_PersistentCooldowns;

// Threat timeout tracking structures
struct ThreatTimeoutInfo
{
    DWORD m_dwAttackerCID;     // Who attacked
    DWORD m_dwVictimCID;       // Who was attacked
    DWORD m_dwLastAttack;      // Last attack timestamp
    int m_lThreatAmount;       // Current threat amount
    void *m_pVictimThreat;     // Pointer to victim's CThreat object (pThis)
    void *m_pAttackerCreature; // Pointer to attacker's creature object (pAttackCreature)
    float m_fDeathPosX;        // Death position X (where victim died)
    float m_fDeathPosZ;        // Death position Z (where victim died)
    bool m_bVictimDead;        // Is the victim dead?
    DWORD m_dwDeathTime;       // When the victim died
};

// Storage for threat timeouts - maps victim CID to list of attackers
std::unordered_map<DWORD, std::vector<ThreatTimeoutInfo>> g_ThreatTimeouts;

// SaveEnemyInfo structure
struct SaveEnemyInfo
{
    DWORD m_dwCID;       // Character ID
    DWORD m_dwTickCount; // Timestamp
};

// Function pointer type for SaveEnemy
typedef char(__thiscall *tSaveEnemy)(void *pThis, unsigned int dwCID);
tSaveEnemy oSaveEnemy = nullptr;

// Function pointer type for AddToThreatList
typedef void(__thiscall *tAddToThreatList)(void *pThis, void *pAttackCreature, int lThreatAmount);
tAddToThreatList oAddToThreatList = nullptr;

// Function pointer type for DeleteThreat - to remove from game's m_ThreatList
typedef char(__thiscall *tDeleteThreat)(void *pThis, void *pAttackCreature);
tDeleteThreat oDeleteThreat = nullptr;

// Function pointers for threat list reading functions
typedef char(__thiscall *tDivisionFame)(void *pThis);
typedef void *(__thiscall *tGetTarget)(void *pThis);
typedef unsigned int *(__thiscall *tGetAward)(void *pThis, unsigned int *dwItemKind, unsigned int *dwOwnerID);

tDivisionFame oDivisionFame = nullptr;
tGetTarget oGetTarget = nullptr;
tGetAward oGetAward = nullptr;

// Function pointer for character death detection
typedef int(__thiscall *tCharacterDead)(void *pThis, void *pOffencer);
tCharacterDead oCharacterDead = nullptr;

// CThreat structure offsets (from PDB analysis)
#define CTHREAT_THREATLIST_OFFSET 0x00 // m_ThreatList is at offset 0
#define CTHREAT_MYHEAD_OFFSET 0x00     // _Myhead is first member
#define CTHREAT_MYSIZE_OFFSET 0x04     // _Mysize is second member

// ThreatInfo structure (from PDB)
struct GameThreatInfo
{
    void *m_pCreature;   // CAggresiveCreature pointer
    int m_lThreatAmount; // Threat amount
};

// List node structure (simplified)
struct ThreatListNode
{
    ThreatListNode *_Next;
    ThreatListNode *_Prev;
    GameThreatInfo _Myval;
};

// Original function bytes storage for unhooking
BYTE g_OriginalBytes[6] = {0};
bool g_HookInstalled = false;

// Function to read an integer value from an INI file
int ReadIniInt(const std::string &iniPath, const std::string &section, const std::string &key, int defaultValue)
{
    return GetPrivateProfileIntA(section.c_str(), key.c_str(), defaultValue, iniPath.c_str());
}

// Function to log threat activity
void LogThreatActivity(const char *format, ...)
{
    if (!g_LogThreatActivity)
        return;

    try
    {
        // Get current time
        SYSTEMTIME st;
        GetLocalTime(&st);

        // Format the message
        char buffer[2048];
        va_list args;
        va_start(args, format);
        vsnprintf(buffer, sizeof(buffer) - 1, format, args);
        va_end(args);
        buffer[sizeof(buffer) - 1] = '\0';

        // Create log entry with timestamp
        char logEntry[2560];
        snprintf(logEntry, sizeof(logEntry) - 1,
                 "[%04d-%02d-%02d %02d:%02d:%02d.%03d] THREAT: %s\r\n",
                 st.wYear, st.wMonth, st.wDay, st.wHour, st.wMinute, st.wSecond, st.wMilliseconds,
                 buffer);
        logEntry[sizeof(logEntry) - 1] = '\0';

        // Write to threat log file
        if (!g_ThreatLogFilePath.empty())
        {
            HANDLE hFile = CreateFileA(g_ThreatLogFilePath.c_str(),
                                       GENERIC_WRITE, FILE_SHARE_READ | FILE_SHARE_WRITE,
                                       NULL, OPEN_ALWAYS, FILE_ATTRIBUTE_NORMAL, NULL);

            if (hFile != INVALID_HANDLE_VALUE)
            {
                SetFilePointer(hFile, 0, NULL, FILE_END);
                DWORD bytesWritten;
                WriteFile(hFile, logEntry, strlen(logEntry), &bytesWritten, NULL);
                CloseHandle(hFile);
            }
        }
    }
    catch (...)
    {
        // Silently handle any logging errors
    }
}

// Function to load configuration from INI file
void LoadConfiguration()
{
    try
    {
        // Get current directory for config file
        char currentDir[MAX_PATH];
        GetCurrentDirectoryA(MAX_PATH, currentDir);
        g_ConfigFilePath = std::string(currentDir) + "\\cfg_killcooldown.ini";

        // Check if the config file exists
        if (GetFileAttributesA(g_ConfigFilePath.c_str()) == INVALID_FILE_ATTRIBUTES)
        {
            return;
        }

        // Read configuration values
        g_Enabled = (ReadIniInt(g_ConfigFilePath, "KillCooldown", "Enabled", 1) != 0);
        g_CooldownTime = ReadIniInt(g_ConfigFilePath, "KillCooldown", "CooldownTime", 300000);
        g_SaveInterval = ReadIniInt(g_ConfigFilePath, "KillCooldown", "SaveInterval", 30000);
        g_IgnoreLatestEnemy = (ReadIniInt(g_ConfigFilePath, "KillCooldown", "IgnoreLatestEnemy", 1) != 0);
        g_StrictConcurrencyProtection = (ReadIniInt(g_ConfigFilePath, "KillCooldown", "StrictConcurrencyProtection", 1) != 0);

        // Read threat timeout configuration
        g_EnableThreatTimeout = (ReadIniInt(g_ConfigFilePath, "ThreatTimeout", "Enabled", 1) != 0);
        g_ThreatTimeout = ReadIniInt(g_ConfigFilePath, "ThreatTimeout", "TimeoutMs", 1800000);
        g_LogThreatActivity = (ReadIniInt(g_ConfigFilePath, "ThreatTimeout", "LogActivity", 1) != 0);

        // Set up threat log file path in logs folder
        g_ThreatLogFilePath = std::string(currentDir) + "\\logs\\threat_activity.log";

        // Create logs directory if it doesn't exist
        CreateDirectoryA("logs", NULL);

        // Log the configuration for debugging
        LogThreatActivity("=== KILLCOOLDOWN PLUGIN LOADED ===");
        LogThreatActivity("Plugin Enabled: %s", g_Enabled ? "YES" : "NO");
        LogThreatActivity("Threat Timeout Enabled: %s", g_EnableThreatTimeout ? "YES" : "NO");
        LogThreatActivity("Threat Timeout: %d ms (%d seconds)", g_ThreatTimeout, g_ThreatTimeout / 1000);
        LogThreatActivity("Log File Path: %s", g_ThreatLogFilePath.c_str());
        LogThreatActivity("================================");

        // Initialize DeleteThreat function pointer for removing from game's m_ThreatList
        oDeleteThreat = (tDeleteThreat)0x0045AAF0; // CThreat::DeleteThreat address from PDB
        LogThreatActivity("DeleteThreat function initialized at 0x0045AAF0");

        // Initialize threat reading function pointers
        oDivisionFame = (tDivisionFame)0x0045C4A0; // CThreat::DivisionFame
        oGetTarget = (tGetTarget)0x0045C250;       // CThreat::GetTarget
        oGetAward = (tGetAward)0x0045B270;         // CThreat::GetAward
        LogThreatActivity("Threat reading functions initialized");

        // Re-enable threat reading hooks with correct addresses from PDB
        LogThreatActivity("Installing threat reading hooks with verified PDB addresses...");

        // DISABLE DivisionFame hook - might be causing crashes on kill
        LogThreatActivity("DivisionFame hook DISABLED - testing for crash prevention");
        DWORD oldProtect; // Keep variable for other hooks

        // DISABLE GetTarget hook too - focus on stable AddToThreatList only
        LogThreatActivity("GetTarget hook DISABLED - using only AddToThreatList for stability");

        // DISABLE GetAward hook - might be causing crashes on kill
        LogThreatActivity("GetAward hook DISABLED - testing for crash prevention");

        // Initialize and hook character death function
        oCharacterDead = (tCharacterDead)0x0041C480; // CCharacter::Dead address from PDB
        LogThreatActivity("Character death function initialized at 0x0041C480");

        // DISABLE character death hook - it's causing crashes
        LogThreatActivity("Character death hook DISABLED - causes crashes when killing");
        LogThreatActivity("Will focus on stable threat timeout without death detection");

        // Run initial cleanup to clear any old data
        if (g_EnableThreatTimeout)
        {
            LogThreatActivity("Running initial threat cleanup...");
            CleanExpiredThreats();
        }
    }
    catch (...)
    {
        // Silently fail if configuration loading fails
    }
}

// Function to add or update threat timeout entry
void AddThreatTimeout(DWORD victimCID, DWORD attackerCID, int threatAmount, void *pVictimThreat = nullptr, void *pAttackerCreature = nullptr)
{
    if (!g_EnableThreatTimeout)
        return;

    try
    {
        std::lock_guard<std::mutex> lock(g_ThreatMutex);

        DWORD currentTime = GetTickCount();

        // Find or create victim entry
        auto &attackerList = g_ThreatTimeouts[victimCID];

        // Look for existing attacker entry
        bool found = false;
        for (auto &info : attackerList)
        {
            if (info.m_dwAttackerCID == attackerCID)
            {
                // Update existing entry
                info.m_dwLastAttack = currentTime;
                info.m_lThreatAmount = threatAmount;
                // Update pointers if provided
                if (pVictimThreat)
                    info.m_pVictimThreat = pVictimThreat;
                if (pAttackerCreature)
                    info.m_pAttackerCreature = pAttackerCreature;
                found = true;

                LogThreatActivity("Updated threat: Victim=0x%08X, Attacker=0x%08X, Amount=%d",
                                  victimCID, attackerCID, threatAmount);
                break;
            }
        }

        if (!found)
        {
            // Add new entry
            ThreatTimeoutInfo newInfo;
            newInfo.m_dwAttackerCID = attackerCID;
            newInfo.m_dwVictimCID = victimCID;
            newInfo.m_dwLastAttack = currentTime;
            newInfo.m_lThreatAmount = threatAmount;
            newInfo.m_pVictimThreat = pVictimThreat;
            newInfo.m_pAttackerCreature = pAttackerCreature;
            newInfo.m_fDeathPosX = 0.0f;
            newInfo.m_fDeathPosZ = 0.0f;
            newInfo.m_bVictimDead = false;
            newInfo.m_dwDeathTime = 0;

            attackerList.push_back(newInfo);

            LogThreatActivity("Added new threat: Victim=0x%08X, Attacker=0x%08X, Amount=%d",
                              victimCID, attackerCID, threatAmount);
        }
    }
    catch (...)
    {
        // Silently handle errors
    }
}

// Function to directly remove expired entries from game's threat list
void DirectCleanGameThreatList(void *pVictimThreat, DWORD victimCID)
{
    if (!pVictimThreat || !g_EnableThreatTimeout)
        return;

    try
    {
        // Get pointer to the threat list head
        ThreatListNode **ppHead = (ThreatListNode **)((BYTE *)pVictimThreat + CTHREAT_MYHEAD_OFFSET);
        DWORD *pSize = (DWORD *)((BYTE *)pVictimThreat + CTHREAT_MYSIZE_OFFSET);

        if (IsBadReadPtr(ppHead, sizeof(void *)) || IsBadReadPtr(pSize, sizeof(DWORD)))
        {
            LogThreatActivity("Invalid threat list pointers for victim 0x%08X", victimCID);
            return;
        }

        ThreatListNode *pHead = *ppHead;
        if (!pHead || IsBadReadPtr(pHead, sizeof(ThreatListNode)))
        {
            LogThreatActivity("Invalid threat list head for victim 0x%08X", victimCID);
            return;
        }

        DWORD currentTime = GetTickCount();
        int removedCount = 0;

        // Walk through the threat list
        ThreatListNode *pCurrent = pHead->_Next;
        while (pCurrent && pCurrent != pHead)
        {
            ThreatListNode *pNext = pCurrent->_Next; // Store next before potential deletion

            if (IsBadReadPtr(pCurrent, sizeof(ThreatListNode)))
                break;

            // Get creature pointer and try to find corresponding CID
            void *pCreature = pCurrent->_Myval.m_pCreature;
            if (pCreature)
            {
                DWORD attackerCID = 0;
                if (SafeReadMemory((PBYTE)pCreature + 0x20, &attackerCID, sizeof(DWORD)) && attackerCID != 0)
                {
                    // Check if this attacker has expired
                    auto victimIt = g_ThreatTimeouts.find(victimCID);
                    if (victimIt != g_ThreatTimeouts.end())
                    {
                        for (const auto &info : victimIt->second)
                        {
                            if (info.m_dwAttackerCID == attackerCID)
                            {
                                DWORD age = currentTime - info.m_dwLastAttack;
                                if (age >= g_ThreatTimeout)
                                {
                                    // Remove this node from the list
                                    LogThreatActivity("Directly removing expired threat: Victim=0x%08X, Attacker=0x%08X, Age=%dms",
                                                      victimCID, attackerCID, age);

                                    // Unlink the node
                                    if (pCurrent->_Prev)
                                        pCurrent->_Prev->_Next = pCurrent->_Next;
                                    if (pCurrent->_Next)
                                        pCurrent->_Next->_Prev = pCurrent->_Prev;

                                    // Free the node (using operator delete like the game does)
                                    delete pCurrent;

                                    // Decrease list size
                                    (*pSize)--;
                                    removedCount++;

                                    LogThreatActivity("Successfully removed threat node from game list");
                                }
                                break;
                            }
                        }
                    }
                }
            }

            pCurrent = pNext;
        }

        if (removedCount > 0)
        {
            LogThreatActivity("Direct cleanup: Removed %d expired threats from game list for victim 0x%08X",
                              removedCount, victimCID);
        }
    }
    catch (...)
    {
        LogThreatActivity("Exception in DirectCleanGameThreatList for victim 0x%08X", victimCID);
    }
}

// Function to clean expired threat entries
void CleanExpiredThreats()
{
    if (!g_EnableThreatTimeout)
        return;

    try
    {
        std::lock_guard<std::mutex> lock(g_ThreatMutex);

        DWORD currentTime = GetTickCount();
        int totalCleaned = 0;

        for (auto it = g_ThreatTimeouts.begin(); it != g_ThreatTimeouts.end();)
        {
            auto &attackerList = it->second;
            DWORD victimCID = it->first;

            // Try to directly clean the game's threat list for this victim
            bool hasValidVictim = false;
            void *pVictimThreat = nullptr;

            for (const auto &info : attackerList)
            {
                if (info.m_pVictimThreat)
                {
                    pVictimThreat = info.m_pVictimThreat;
                    hasValidVictim = true;
                    break;
                }
            }

            if (hasValidVictim && pVictimThreat)
            {
                DirectCleanGameThreatList(pVictimThreat, victimCID);
            }

            // Remove expired attackers from our tracking
            auto attackerIt = attackerList.begin();
            while (attackerIt != attackerList.end())
            {
                DWORD age = currentTime - attackerIt->m_dwLastAttack;

                if (age >= g_ThreatTimeout)
                {
                    LogThreatActivity("Expired threat: Victim=0x%08X, Attacker=0x%08X, Age=%dms",
                                      it->first, attackerIt->m_dwAttackerCID, age);

                    attackerIt = attackerList.erase(attackerIt);
                    totalCleaned++;
                }
                else
                {
                    ++attackerIt;
                }
            }

            // Remove victim entry if no attackers left
            if (attackerList.empty())
            {
                LogThreatActivity("Removed empty victim entry: 0x%08X", it->first);
                it = g_ThreatTimeouts.erase(it);
            }
            else
            {
                ++it;
            }
        }

        if (totalCleaned > 0)
        {
            LogThreatActivity("Cleanup completed: %d expired threats removed (timeout: %d seconds)", totalCleaned, g_ThreatTimeout / 1000);
        }
        else
        {
            LogThreatActivity("Cleanup completed: No expired threats found");
        }

        // Log statistics every 10 cleanups
        static int statsCounter = 0;
        if (++statsCounter >= 10)
        {
            statsCounter = 0;
            LogThreatStatistics();
        }
    }
    catch (...)
    {
        // Silently handle errors
    }
}

// Function to mark victim as dead and record death position
void MarkVictimDead(DWORD victimCID, float deathPosX, float deathPosZ)
{
    if (!g_EnableThreatTimeout)
        return;

    try
    {
        std::lock_guard<std::mutex> lock(g_ThreatMutex);

        auto it = g_ThreatTimeouts.find(victimCID);
        if (it != g_ThreatTimeouts.end())
        {
            DWORD currentTime = GetTickCount();
            int attackerCount = it->second.size();

            // Mark all attackers' victim as dead and record death position
            for (auto &info : it->second)
            {
                info.m_bVictimDead = true;
                info.m_dwDeathTime = currentTime;
                info.m_fDeathPosX = deathPosX;
                info.m_fDeathPosZ = deathPosZ;
            }

            LogThreatActivity("DEATH EVENT: Victim=0x%08X died at position (%.2f, %.2f) with %d attackers",
                              victimCID, deathPosX, deathPosZ, attackerCount);
        }
    }
    catch (...)
    {
        // Silently handle errors
    }
}

// Function to remove all threats for a specific victim (when creature dies)
void RemoveVictimThreats(DWORD victimCID)
{
    if (!g_EnableThreatTimeout)
        return;

    try
    {
        std::lock_guard<std::mutex> lock(g_ThreatMutex);

        auto it = g_ThreatTimeouts.find(victimCID);
        if (it != g_ThreatTimeouts.end())
        {
            int attackerCount = it->second.size();
            g_ThreatTimeouts.erase(it);

            LogThreatActivity("Removed victim on death: 0x%08X (%d attackers)",
                              victimCID, attackerCount);
        }
    }
    catch (...)
    {
        // Silently handle errors
    }
}

// Function to get threat statistics for debugging
void LogThreatStatistics()
{
    if (!g_EnableThreatTimeout)
        return;

    try
    {
        std::lock_guard<std::mutex> lock(g_ThreatMutex);

        int totalVictims = g_ThreatTimeouts.size();
        int totalAttackers = 0;

        for (const auto &victim : g_ThreatTimeouts)
        {
            totalAttackers += victim.second.size();
        }

        LogThreatActivity("=== THREAT STATISTICS ===");
        LogThreatActivity("Total Victims: %d", totalVictims);
        LogThreatActivity("Total Attack Records: %d", totalAttackers);
        LogThreatActivity("Timeout Setting: %d ms", g_ThreatTimeout);
        LogThreatActivity("========================");

        // Log detailed info for each victim (limit to first 10 for readability)
        int count = 0;
        for (const auto &victim : g_ThreatTimeouts)
        {
            if (count >= 10)
                break;

            LogThreatActivity("Victim 0x%08X has %d attackers:",
                              victim.first, (int)victim.second.size());

            for (const auto &attacker : victim.second)
            {
                DWORD age = GetTickCount() - attacker.m_dwLastAttack;
                LogThreatActivity("  - Attacker 0x%08X: %d threat, %d ms ago",
                                  attacker.m_dwAttackerCID, attacker.m_lThreatAmount, age);
            }
            count++;
        }
    }
    catch (...)
    {
        // Silently handle errors
    }
}

// Function to save cooldowns to file
void SaveCooldownsToFile()
{
    try
    {
        std::lock_guard<std::mutex> lock(g_CooldownMutex);

        // Check if we have any cooldowns to save
        if (g_PersistentCooldowns.empty())
        {
            return;
        }

        std::ofstream file(g_CooldownFilePath, std::ios::binary);
        if (!file.is_open())
        {
            return;
        }

        // Count active cooldowns
        DWORD currentTime = GetTickCount();
        size_t activeCount = 0;
        for (const auto &entry : g_PersistentCooldowns)
        {
            DWORD age = currentTime - entry.second;
            if (age < g_CooldownTime)
            {
                activeCount++;
            }
        }

        // Write the number of entries
        size_t count = g_PersistentCooldowns.size();
        file.write(reinterpret_cast<const char *>(&count), sizeof(count));

        // Write each entry
        for (const auto &entry : g_PersistentCooldowns)
        {
            file.write(reinterpret_cast<const char *>(&entry.first), sizeof(entry.first));
            file.write(reinterpret_cast<const char *>(&entry.second), sizeof(entry.second));
        }

        file.close();
    }
    catch (...)
    {
        // Silently fail if saving fails
    }
}

// Function to load cooldowns from file
void LoadCooldownsFromFile()
{
    try
    {
        std::lock_guard<std::mutex> lock(g_CooldownMutex);

        std::ifstream file(g_CooldownFilePath, std::ios::binary);
        if (!file.is_open())
        {
            return;
        }

        // Read the number of entries
        size_t count = 0;
        file.read(reinterpret_cast<char *>(&count), sizeof(count));

        if (count == 0)
        {
            file.close();
            return;
        }

        // Read each entry
        g_PersistentCooldowns.clear();
        size_t validCount = 0;
        DWORD currentTime = GetTickCount();

        for (size_t i = 0; i < count; i++)
        {
            DWORD characterId = 0;
            DWORD timestamp = 0;

            file.read(reinterpret_cast<char *>(&characterId), sizeof(characterId));
            file.read(reinterpret_cast<char *>(&timestamp), sizeof(timestamp));

            // Check if the cooldown is still valid
            DWORD age = currentTime - timestamp;
            if (age < g_CooldownTime)
            {
                g_PersistentCooldowns[characterId] = timestamp;
                validCount++;
            }
        }

        file.close();
    }
    catch (...)
    {
        // Silently fail if loading fails
    }
}

// Thread function to periodically save cooldowns
DWORD WINAPI CooldownSaveThread(LPVOID)
{
    while (true)
    {
        // Save cooldowns at the configured interval
        Sleep(g_SaveInterval);

        try
        {
            // Check if an immediate save was requested
            bool saveNeeded = false;
            {
                // Use a critical section to safely check and reset the flag
                static std::mutex saveFlagMutex;
                std::lock_guard<std::mutex> lock(saveFlagMutex);
                if (g_SaveRequested)
                {
                    saveNeeded = true;
                    g_SaveRequested = false;
                }
            }

            // Always save periodically or when requested
            SaveCooldownsToFile();
        }
        catch (...)
        {
            // Silently continue if saving fails
        }
    }
    return 0;
}

// Helper function to safely access memory
// This function is kept simple with no C++ objects to avoid unwinding issues
BOOL SafeReadMemory(LPVOID lpAddress, LPVOID lpBuffer, SIZE_T nSize)
{
    if (!lpAddress || !lpBuffer || nSize == 0)
    {
        return FALSE;
    }

    BOOL result = FALSE;

    __try
    {
        memcpy(lpBuffer, lpAddress, nSize);
        result = TRUE;
    }
    __except (EXCEPTION_EXECUTE_HANDLER)
    {
        result = FALSE;
    }

    return result;
}

// Helper function to safely check if a pointer is valid
// This function is kept simple with no C++ objects to avoid unwinding issues
BOOL IsPtrValid(LPVOID ptr, SIZE_T size)
{
    if (!ptr)
        return FALSE;

    BOOL result = FALSE;

    __try
    {
        volatile BYTE temp = *((BYTE *)ptr);
        volatile BYTE temp2 = *(((BYTE *)ptr) + size - 1);
        result = TRUE;
    }
    __except (EXCEPTION_EXECUTE_HANDLER)
    {
        result = FALSE;
    }

    return result;
}

// Helper function to safely read the m_LatestEnemy pointer
// This function is kept simple with no C++ objects to avoid unwinding issues
SaveEnemyInfo *ReadLatestEnemyPtr(void *pThis)
{
    if (!pThis)
        return nullptr;

    SaveEnemyInfo *latestEnemy = nullptr;

    __try
    {
        // Try different potential offsets for m_LatestEnemy
        // Common offsets in different game versions
        DWORD possibleOffsets[] = {0x8, 0xC, 0x10, 0x14, 0x18, 0x1C, 0x20};

        for (int i = 0; i < sizeof(possibleOffsets) / sizeof(DWORD); i++)
        {
            PBYTE pOffset = (PBYTE)pThis + possibleOffsets[i];

            // Read the pointer value
            SaveEnemyInfo *testPtr = nullptr;
            if (!SafeReadMemory(pOffset, &testPtr, sizeof(testPtr)) || !testPtr)
            {
                continue;
            }

            // Simple validation - just check if we can read the first byte
            if (IsPtrValid(testPtr, sizeof(SaveEnemyInfo) * 5))
            {
                latestEnemy = testPtr;
                break;
            }
        }
    }
    __except (EXCEPTION_EXECUTE_HANDLER)
    {
        return nullptr;
    }

    return latestEnemy;
}

// Helper function to safely read m_LatestEnemy entries
// This function is kept simple with no C++ objects to avoid unwinding issues
void ReadLatestEnemyEntries(SaveEnemyInfo *latestEnemy)
{
    if (!latestEnemy)
        return;

    __try
    {
        // Just validate the entries but don't log them
        for (int i = 0; i < 5; i++)
        {
            IsPtrValid(&latestEnemy[i], sizeof(SaveEnemyInfo));
        }
    }
    __except (EXCEPTION_EXECUTE_HANDLER)
    {
        // Silent exception handling
    }
}

// Helper function to safely access the m_LatestEnemy field
SaveEnemyInfo *GetLatestEnemyArray(void *pThis)
{
    if (!pThis)
    {
        return nullptr;
    }

    // Call the exception-safe helper function
    SaveEnemyInfo *latestEnemy = nullptr;

    try
    {
        latestEnemy = ReadLatestEnemyPtr(pThis);

        if (latestEnemy)
        {
            // Call the helper function to read entries
            ReadLatestEnemyEntries(latestEnemy);
        }
    }
    catch (...)
    {
        return nullptr;
    }

    return latestEnemy;
}

// Helper function to safely check if CID exists in array
// This function is kept simple with no C++ objects to avoid unwinding issues
BOOL CheckCidInArray(SaveEnemyInfo *latestEnemy, DWORD dwCID, int *foundIndex)
{
    if (!latestEnemy || !foundIndex)
        return FALSE;

    BOOL result = FALSE;
    *foundIndex = -1;

    __try
    {
        for (int i = 0; i < 5; i++)
        {
            // Simple validation before access
            if (!IsPtrValid(&latestEnemy[i], sizeof(SaveEnemyInfo)))
            {
                break;
            }

            if (latestEnemy[i].m_dwCID == dwCID)
            {
                result = TRUE;
                *foundIndex = i;
                break;
            }
        }
    }
    __except (EXCEPTION_EXECUTE_HANDLER)
    {
        result = FALSE;
    }

    return result;
}

// Helper function to safely call the original SaveEnemy function
// This function is kept simple with no C++ objects to avoid unwinding issues
char CallOriginalSaveEnemy(void *pThis, DWORD dwCID)
{
    if (!oSaveEnemy || !pThis)
    {
        return 0;
    }

    char result = 0;

    __try
    {
        result = oSaveEnemy(pThis, dwCID);
    }
    __except (EXCEPTION_EXECUTE_HANDLER)
    {
        // Exception caught - silent handling
    }

    return result;
}

// Hook function for CThreat::SaveEnemy
char __fastcall Hook_SaveEnemy(void *pThis, void *edx, unsigned int dwCID)
{
    // Skip if plugin is disabled
    if (!g_Enabled)
    {
        return oSaveEnemy ? CallOriginalSaveEnemy(pThis, dwCID) : 0;
    }

    // Safety check for null pointer
    if (!pThis)
    {
        return 0;
    }

    // Skip processing for invalid CIDs
    if (dwCID == 0)
    {
        return oSaveEnemy ? CallOriginalSaveEnemy(pThis, dwCID) : 0;
    }

    // Use a lock to prevent concurrent/recursive calls if strict concurrency protection is enabled
    bool lockAcquired = false;
    if (g_StrictConcurrencyProtection)
    {
        lockAcquired = g_CallMutex.try_lock();
        if (!lockAcquired)
        {
            // Instead of always returning 1 (cooldown), check if this CID has a persistent cooldown
            bool hasCooldown = false;
            {
                std::lock_guard<std::mutex> lock(g_CooldownMutex);
                auto it = g_PersistentCooldowns.find(dwCID);
                if (it != g_PersistentCooldowns.end())
                {
                    DWORD age = GetTickCount() - it->second;
                    hasCooldown = (age < g_CooldownTime);
                }
            }

            return hasCooldown ? 1 : 0; // Return cooldown status based on actual cooldown
        }

        // Set processing flag to detect recursive calls
        if (g_ProcessingCall)
        {
            // Instead of always returning 1 (cooldown), check if this CID has a persistent cooldown
            bool hasCooldown = false;
            {
                std::lock_guard<std::mutex> lock(g_CooldownMutex);
                auto it = g_PersistentCooldowns.find(dwCID);
                if (it != g_PersistentCooldowns.end())
                {
                    DWORD age = GetTickCount() - it->second;
                    hasCooldown = (age < g_CooldownTime);
                }
            }

            g_CallMutex.unlock();
            return hasCooldown ? 1 : 0; // Return cooldown status based on actual cooldown
        }

        g_ProcessingCall = true;
    }

    // Declare variables at function start to avoid initialization issues
    DWORD startTime = 0;
    bool hasPersistentCooldown = false;
    bool cidFoundInArray = false;
    int foundIndex = -1;
    char result = 0; // Default to 0 (no cooldown) for new kills
    bool overrideResult = false;
    SaveEnemyInfo *latestEnemy = nullptr;
    bool cooldownUpdated = false;

    try
    {
        // Get current timestamp
        startTime = GetTickCount();

        // Check persistent cooldown
        {
            std::lock_guard<std::mutex> lock(g_CooldownMutex);
            auto it = g_PersistentCooldowns.find(dwCID);
            if (it != g_PersistentCooldowns.end())
            {
                DWORD age = startTime - it->second;
                if (age < g_CooldownTime)
                {
                    hasPersistentCooldown = true;
                }
            }
        }

        // Only check latestEnemy if not ignoring it
        if (!g_IgnoreLatestEnemy)
        {
            // Get the array of SaveEnemyInfo from the CThreat object
            latestEnemy = GetLatestEnemyArray(pThis);

            if (latestEnemy)
            {
                // Use the exception-safe helper function
                if (CheckCidInArray(latestEnemy, dwCID, &foundIndex))
                {
                    cidFoundInArray = true;
                }
            }
        }

        // Determine what result we should return
        if (cidFoundInArray)
        {
            // CID is already in array, should return 1
            result = 1;
            overrideResult = true; // Always override when we know the state
        }
        else if (hasPersistentCooldown)
        {
            // CID has persistent cooldown but not in array (player relogged)
            // Override to 1 to prevent fame gain
            result = 1; // Return 1 to indicate cooldown
            overrideResult = true;
        }
        else
        {
            // No cooldown found, this is a new kill
            result = 0;
            overrideResult = false;
        }
    }
    catch (...)
    {
        // Silently handle exceptions
    }

    // Call original function - outside of try block to avoid unwinding issues
    if (!overrideResult && oSaveEnemy && pThis)
    {
        try
        {
            result = CallOriginalSaveEnemy(pThis, dwCID);
        }
        catch (...)
        {
            result = 0; // Default to "new kill" if original function fails
        }
    }

    try
    {
        // Update the persistent cooldown timestamp when a new kill happens (result = 0)
        if (result == 0)
        {
            {
                std::lock_guard<std::mutex> lock(g_CooldownMutex);
                g_PersistentCooldowns[dwCID] = startTime;
                cooldownUpdated = true;
            }

            // Request a save but don't do it immediately to avoid deadlock
            // We'll set a flag to save on the next timer tick
            {
                static std::mutex saveFlagMutex;
                std::lock_guard<std::mutex> lock(saveFlagMutex);
                g_SaveRequested = true;
            }
        }
    }
    catch (...)
    {
        // Silently handle exceptions
    }

    // Reset processing flag and unlock mutex if we acquired it
    if (g_StrictConcurrencyProtection && lockAcquired)
    {
        g_ProcessingCall = false;
        g_CallMutex.unlock();
    }

    return result;
}

// Function to clean expired entries from threat list before game reads it
void CleanExpiredFromGameThreatList(void *pThis)
{
    if (!pThis || !g_EnableThreatTimeout)
        return;

    try
    {
        // Get victim CID from the CThreat object
        DWORD victimCID = 0;
        if (SafeReadMemory((PBYTE)pThis + 0x04, &victimCID, sizeof(DWORD)) && victimCID != 0)
        {
            // Get pointer to the threat list
            ThreatListNode **ppHead = (ThreatListNode **)((BYTE *)pThis + CTHREAT_MYHEAD_OFFSET);
            DWORD *pSize = (DWORD *)((BYTE *)pThis + CTHREAT_MYSIZE_OFFSET);

            if (IsBadReadPtr(ppHead, sizeof(void *)) || IsBadReadPtr(pSize, sizeof(DWORD)))
                return;

            ThreatListNode *pHead = *ppHead;
            if (!pHead || IsBadReadPtr(pHead, sizeof(ThreatListNode)))
                return;

            DWORD currentTime = GetTickCount();
            int removedCount = 0;

            // Walk through the threat list and remove expired entries
            ThreatListNode *pCurrent = pHead->_Next;
            while (pCurrent && pCurrent != pHead)
            {
                ThreatListNode *pNext = pCurrent->_Next;

                if (IsBadReadPtr(pCurrent, sizeof(ThreatListNode)))
                    break;

                // Get creature pointer and CID
                void *pCreature = pCurrent->_Myval.m_pCreature;
                if (pCreature)
                {
                    DWORD attackerCID = 0;
                    if (SafeReadMemory((PBYTE)pCreature + 0x20, &attackerCID, sizeof(DWORD)) && attackerCID != 0)
                    {
                        // Check if this attacker should be expired (time-based)
                        std::lock_guard<std::mutex> lock(g_ThreatMutex);
                        auto victimIt = g_ThreatTimeouts.find(victimCID);
                        if (victimIt != g_ThreatTimeouts.end())
                        {
                            for (const auto &info : victimIt->second)
                            {
                                if (info.m_dwAttackerCID == attackerCID)
                                {
                                    // Simple time-based expiration
                                    DWORD age = currentTime - info.m_dwLastAttack;
                                    if (age >= g_ThreatTimeout)
                                    {
                                        LogThreatActivity("REMOVING expired threat from game list: Victim=0x%08X, Attacker=0x%08X, Age=%dms (timeout=%dms)",
                                                          victimCID, attackerCID, age, g_ThreatTimeout);

                                        // Unlink the node
                                        if (pCurrent->_Prev)
                                            pCurrent->_Prev->_Next = pCurrent->_Next;
                                        if (pCurrent->_Next)
                                            pCurrent->_Next->_Prev = pCurrent->_Prev;

                                        // Free the node
                                        delete pCurrent;

                                        // Decrease list size
                                        (*pSize)--;
                                        removedCount++;
                                    }
                                    break;
                                }
                            }
                        }
                    }
                }

                pCurrent = pNext;
            }

            if (removedCount > 0)
            {
                LogThreatActivity("Cleaned %d expired threats from game list before read operation", removedCount);
            }
        }
    }
    catch (...)
    {
        LogThreatActivity("Exception in CleanExpiredFromGameThreatList");
    }
}

// Hook for CThreat::DivisionFame - logs and calls original
char __fastcall Hook_DivisionFame(void *pThis, void *edx)
{
    LogThreatActivity("DivisionFame called for CThreat object: 0x%08X", (DWORD)pThis);

    // Call original function
    if (oDivisionFame)
    {
        char result = oDivisionFame(pThis);
        LogThreatActivity("DivisionFame completed, result: %d", result);
        return result;
    }
    return 0;
}

// Hook for CThreat::GetTarget - logs and calls original
void *__fastcall Hook_GetTarget(void *pThis, void *edx)
{
    LogThreatActivity("GetTarget called for CThreat object: 0x%08X", (DWORD)pThis);

    // Call original function
    if (oGetTarget)
    {
        void *result = oGetTarget(pThis);
        LogThreatActivity("GetTarget completed, target: 0x%08X", (DWORD)result);
        return result;
    }
    return nullptr;
}

// Hook for CThreat::GetAward - logs and calls original
unsigned int *__fastcall Hook_GetAward(void *pThis, void *edx, unsigned int *dwItemKind, unsigned int *dwOwnerID)
{
    LogThreatActivity("GetAward called for CThreat object: 0x%08X", (DWORD)pThis);

    // Call original function
    if (oGetAward)
    {
        unsigned int *result = oGetAward(pThis, dwItemKind, dwOwnerID);
        LogThreatActivity("GetAward completed, result: 0x%08X", (DWORD)result);
        return result;
    }
    return nullptr;
}

// Static set to track recently processed deaths (prevent spam)
static std::unordered_set<DWORD> g_RecentDeaths;
static std::mutex g_DeathMutex;

// Hook for CCharacter::Dead - simple logging and original call
int __fastcall Hook_CharacterDead(void *pThis, void *edx, void *pOffencer)
{
    LogThreatActivity("CHARACTER DEATH HOOK: pThis=0x%08X, pOffencer=0x%08X", (DWORD)pThis, (DWORD)pOffencer);

    // Call original function
    int result = 1;
    if (oCharacterDead)
    {
        result = oCharacterDead(pThis, pOffencer);
        LogThreatActivity("Character death processed, result: %d", result);
    }

    return result;
}

// SAFE tracking-only function - no blocking, no memory manipulation
void __fastcall Hook_AddToThreatList(void *pThis, void *edx, void *pAttackCreature, int lThreatAmount)
{
    static int hookCallCount = 0;
    hookCallCount++;

    // Log the hook call
    LogThreatActivity("SAFE Hook #%d: pThis=0x%08X, pAttackCreature=0x%08X, lThreatAmount=%d",
                      hookCallCount, (DWORD)pThis, (DWORD)pAttackCreature, lThreatAmount);

    // Extract CIDs for tracking only
    DWORD victimCID = (DWORD)pThis;
    DWORD attackerCID = (DWORD)pAttackCreature;

    // Try to read actual CIDs
    DWORD tempCID;
    if (SafeReadMemory((PBYTE)pThis + 0x20, &tempCID, sizeof(DWORD)) && tempCID != 0)
        victimCID = tempCID;
    if (SafeReadMemory((PBYTE)pAttackCreature + 0x20, &tempCID, sizeof(DWORD)) && tempCID != 0)
        attackerCID = tempCID;

    // Just track the attack - no blocking or manipulation
    if (g_EnableThreatTimeout)
    {
        LogThreatActivity("Attack: Victim=0x%08X, Attacker=0x%08X, Amount=%d", victimCID, attackerCID, lThreatAmount);
        AddThreatTimeout(victimCID, attackerCID, lThreatAmount, pThis, pAttackCreature);
    }

    // ALWAYS call original function - no interference with game logic
    if (oAddToThreatList)
    {
        try
        {
            // Temporarily restore original bytes
            DWORD oldProtect;
            VirtualProtect((LPVOID)oAddToThreatList, 6, PAGE_EXECUTE_READWRITE, &oldProtect);
            memcpy((LPVOID)oAddToThreatList, g_OriginalBytes, 6);
            VirtualProtect((LPVOID)oAddToThreatList, 6, oldProtect, &oldProtect);

            // Call original function
            ((tAddToThreatList)oAddToThreatList)(pThis, pAttackCreature, lThreatAmount);

            // Restore our hook
            VirtualProtect((LPVOID)oAddToThreatList, 6, PAGE_EXECUTE_READWRITE, &oldProtect);
            Detours::DetourFunction((PBYTE)oAddToThreatList, (PBYTE)Hook_AddToThreatList, 6);
            VirtualProtect((LPVOID)oAddToThreatList, 6, oldProtect, &oldProtect);

            LogThreatActivity("Original function called successfully");
        }
        catch (...)
        {
            LogThreatActivity("Exception calling original function");
        }
    }

    // Periodically clean expired threats from our tracking (every 10 calls for more responsive cleanup)
    static int cleanupCounter = 0;
    if (++cleanupCounter >= 10)
    {
        cleanupCounter = 0;
        LogThreatActivity("Running periodic tracking cleanup...");
        CleanExpiredThreats();

        // Character deaths are now handled by the CCharacter::Dead hook
        // Clean up recent deaths set to prevent memory growth
        {
            std::lock_guard<std::mutex> lock(g_DeathMutex);
            g_RecentDeaths.clear();
            LogThreatActivity("Cleared recent deaths tracking set");
        }
    }
}

// DISABLED - C++ function to process the threat data safely
void __stdcall ProcessThreatHook(void *pThis, void *pAttackCreature, int lThreatAmount)
{
    // DISABLED FOR TESTING - ALL THREAT PROCESSING DISABLED
    return;
}

// Function to scan for calls to SaveEnemy in memory
BOOL FindSaveEnemyCalls()
{
    BOOL success = FALSE;

    try
    {
        HMODULE hModule = GetModuleHandleA(NULL);
        if (!hModule)
        {
            return FALSE;
        }

        // Try known addresses for the SaveEnemy function
        DWORD possibleAddresses[] = {
            0x0045A930, // From PDB
            0x0045A950,
            0x0045A910,
            0x0045A900,
            0x0045A940,
            0x0045A920};

        for (int i = 0; i < sizeof(possibleAddresses) / sizeof(DWORD); i++)
        {
            DWORD address = possibleAddresses[i];

            // Store the original function address
            oSaveEnemy = (tSaveEnemy)address;

            // Try to hook the function
            DWORD oldProtect;
            if (VirtualProtect((LPVOID)address, 6, PAGE_EXECUTE_READWRITE, &oldProtect))
            {
                int result = Detours::DetourFunction((PBYTE)address, (PBYTE)Hook_SaveEnemy, 6);

                if (result == DETOUR_SUCCESS)
                {
                    VirtualProtect((LPVOID)address, 6, oldProtect, &oldProtect);
                    success = TRUE;
                    break;
                }

                // Restore protection
                VirtualProtect((LPVOID)address, 6, oldProtect, &oldProtect);
            }
        }

        // If none of the hardcoded addresses worked, try scanning for the function
        if (!success)
        {
            // Get module information
            MODULEINFO moduleInfo;
            if (GetModuleInformation(GetCurrentProcess(), hModule, &moduleInfo, sizeof(moduleInfo)))
            {
                PBYTE moduleStart = (PBYTE)moduleInfo.lpBaseOfDll;
                SIZE_T moduleSize = moduleInfo.SizeOfImage;

                // Simple signature for SaveEnemy function - adjust as needed
                BYTE signature[] = {0x55, 0x8B, 0xEC, 0x83, 0xEC};
                SIZE_T sigSize = sizeof(signature);

                // Scan through the module memory
                for (SIZE_T i = 0; i < moduleSize - sigSize; i++)
                {
                    bool found = true;
                    for (SIZE_T j = 0; j < sigSize; j++)
                    {
                        if (moduleStart[i + j] != signature[j])
                        {
                            found = false;
                            break;
                        }
                    }

                    if (found)
                    {
                        DWORD address = (DWORD)(moduleStart + i);

                        // Store the original function address
                        oSaveEnemy = (tSaveEnemy)address;

                        // Try to hook the function
                        DWORD oldProtect;
                        if (VirtualProtect((LPVOID)address, 6, PAGE_EXECUTE_READWRITE, &oldProtect))
                        {
                            int result = Detours::DetourFunction((PBYTE)address, (PBYTE)Hook_SaveEnemy, 6);

                            if (result == DETOUR_SUCCESS)
                            {
                                VirtualProtect((LPVOID)address, 6, oldProtect, &oldProtect);
                                success = TRUE;
                                break;
                            }

                            // Restore protection
                            VirtualProtect((LPVOID)address, 6, oldProtect, &oldProtect);
                        }
                    }
                }
            }
        }
    }
    catch (...)
    {
        success = FALSE;
    }

    return success;
}

// Function to find and hook AddToThreatList
BOOL FindAddToThreatListCalls()
{
    BOOL success = FALSE;

    try
    {
        LogThreatActivity("Starting AddToThreatList hook installation...");

        // Try known addresses for the AddToThreatList function
        DWORD possibleAddresses[] = {
            0x0045C060, // Confirmed from PDB: CThreat::AddToThreatList
            0x0045C070,
            0x0045C050};

        for (int i = 0; i < sizeof(possibleAddresses) / sizeof(DWORD); i++)
        {
            DWORD address = possibleAddresses[i];
            LogThreatActivity("Trying address 0x%08X...", address);

            // Check if the address is valid
            if (IsBadReadPtr((void *)address, 6))
            {
                LogThreatActivity("Address 0x%08X is invalid (bad read)", address);
                continue;
            }

            // Read and store original bytes before hooking
            BYTE originalBytes[6];
            if (!SafeReadMemory((PBYTE)address, originalBytes, 6))
            {
                LogThreatActivity("Cannot read function bytes at 0x%08X", address);
                continue;
            }

            // Log the function bytes for debugging
            LogThreatActivity("Function bytes at 0x%08X: %02X %02X %02X %02X %02X %02X",
                              address, originalBytes[0], originalBytes[1], originalBytes[2],
                              originalBytes[3], originalBytes[4], originalBytes[5]);

            // Try to hook the function
            DWORD oldProtect;
            if (VirtualProtect((LPVOID)address, 6, PAGE_EXECUTE_READWRITE, &oldProtect))
            {
                LogThreatActivity("Memory protection changed for 0x%08X, attempting hook...", address);

                // Store original bytes and function pointer
                memcpy(g_OriginalBytes, originalBytes, 6);
                oAddToThreatList = (tAddToThreatList)address;

                int result = Detours::DetourFunction((PBYTE)address, (PBYTE)Hook_AddToThreatList, 6);

                if (result == DETOUR_SUCCESS)
                {
                    VirtualProtect((LPVOID)address, 6, oldProtect, &oldProtect);
                    success = TRUE;
                    g_HookInstalled = true;

                    LogThreatActivity("AddToThreatList hook installed successfully at address 0x%08X", address);
                    LogThreatActivity("Original bytes stored: %02X %02X %02X %02X %02X %02X",
                                      g_OriginalBytes[0], g_OriginalBytes[1], g_OriginalBytes[2],
                                      g_OriginalBytes[3], g_OriginalBytes[4], g_OriginalBytes[5]);
                    break;
                }
                else
                {
                    LogThreatActivity("Hook failed at 0x%08X with result %d", address, result);
                    oAddToThreatList = nullptr; // Reset on failure
                    VirtualProtect((LPVOID)address, 6, oldProtect, &oldProtect);
                }
            }
            else
            {
                LogThreatActivity("Failed to change memory protection for 0x%08X (Error: %d)", address, GetLastError());
            }
        }

        if (!success)
        {
            LogThreatActivity("Failed to install AddToThreatList hook at any address");
        }
    }
    catch (...)
    {
        LogThreatActivity("Exception occurred during AddToThreatList hook installation");
    }

    return success;
}

// Main thread function for plugin initialization
DWORD WINAPI MainThread(LPVOID)
{
    try
    {
        // Give the main application time to initialize
        Sleep(3000);

        // Create logs directory
        CreateDirectoryA("logs", NULL);

        // Initialize cooldown file path
        char currentDir[MAX_PATH];
        GetCurrentDirectoryA(MAX_PATH, currentDir);
        g_CooldownFilePath = std::string(currentDir) + "\\logs\\killcooldowns.dat";

        // Load configuration
        LoadConfiguration();

        // Skip further initialization if plugin is disabled
        if (!g_Enabled)
        {
            return 0;
        }

        // Load persistent cooldowns
        LoadCooldownsFromFile();

        // Start cooldown save thread
        CreateThread(NULL, 0, CooldownSaveThread, NULL, 0, NULL);

        // Try to find and hook the SaveEnemy function
        BOOL hookSuccess = FindSaveEnemyCalls();

        // Try to find and hook the AddToThreatList function with safer approach
        BOOL threatHookSuccess = FALSE;
        if (g_EnableThreatTimeout)
        {
            LogThreatActivity("Attempting to install SAFE AddToThreatList hook...");
            threatHookSuccess = FindAddToThreatListCalls();

            if (threatHookSuccess)
            {
                LogThreatActivity("SAFE AddToThreatList hook installation completed successfully");
            }
            else
            {
                LogThreatActivity("SAFE AddToThreatList hook installation FAILED");
            }
        }
        else
        {
            LogThreatActivity("Threat timeout is disabled, skipping AddToThreatList hook");
        }

// For debugging only - remove after confirming hook works
#ifdef _DEBUG
        if (hookSuccess)
        {
            MessageBoxA(NULL, "KillCooldown hook installed successfully!", "KillCooldown Plugin", MB_OK | MB_ICONINFORMATION);
        }
        else
        {
            MessageBoxA(NULL, "KillCooldown hook failed to install!", "KillCooldown Plugin", MB_OK | MB_ICONERROR);
        }

        if (g_EnableThreatTimeout)
        {
            if (threatHookSuccess)
            {
                MessageBoxA(NULL, "ThreatTimeout hook installed successfully!", "KillCooldown Plugin", MB_OK | MB_ICONINFORMATION);
            }
            else
            {
                MessageBoxA(NULL, "ThreatTimeout hook failed to install!", "KillCooldown Plugin", MB_OK | MB_ICONERROR);
            }
        }
#endif
    }
    catch (...)
    {
        // Silently handle exceptions
    }

    return 0;
}

// DllMain function
BOOL APIENTRY DllMain(HMODULE hModule, DWORD reason, LPVOID lpReserved)
{
    switch (reason)
    {
    case DLL_PROCESS_ATTACH:
        DisableThreadLibraryCalls(hModule);

        try
        {
            // Initialize the licensing system
            RYL1Plugin::Initialize();

            // Check license before proceeding
            if (!RYL1Plugin::CheckLicense())
            {
                return FALSE; // This will never be reached due to crash in CheckLicense
            }
        }
        catch (...)
        {
// If license check fails, continue anyway for debugging purposes
#ifdef _DEBUG
            MessageBoxA(NULL, "License check failed but continuing for debug purposes", "KillCooldown Plugin", MB_OK | MB_ICONWARNING);
#endif
        }

        CreateThread(0, 0, MainThread, 0, 0, 0);
        break;

    case DLL_PROCESS_DETACH:
        try
        {
            if (g_Enabled && !g_CooldownFilePath.empty())
            {
                SaveCooldownsToFile();
            }

            // Cleanup the licensing system
            RYL1Plugin::Cleanup();
        }
        catch (...)
        {
            // Silently fail on shutdown
        }
        break;
    }

    return TRUE;
}