[KillCooldown]
; Enable or disable the kill cooldown functionality
Enabled=1

; Cooldown time in milliseconds (default: 300000 = 5 minutes)
CooldownTime=1000

; Save interval for persistent cooldowns in milliseconds (default: 30000 = 30 seconds)
SaveInterval=30000

; Ignore LatestEnemy array to avoid crashes (recommended: 1)
IgnoreLatestEnemy=1

; Enable strict concurrency protection (recommended: 1)
StrictConcurrencyProtection=1

[ThreatTimeout]
; Enable or disable threat timeout functionality
Enabled=1

; Timeout for threat entries in milliseconds (default: 1800000 = 30 minutes)
; After this time, attacks expire and won't count for fame/loot
TimeoutMs=5000

; Enable detailed threat activity logging
LogActivity=1

; Note: Threat timeout tracks who attacked whom and when
; This allows for proper fame distribution when creatures die
; Attacks older than TimeoutMs will be automatically removed
; The system is persistent until creature death
